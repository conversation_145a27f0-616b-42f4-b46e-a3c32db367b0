// Quick test file to verify the date formatting functions work
// This is a temporary test file - you can delete it after verification

import { formatDateByLanguage, formatShortDateByLanguage } from './src/lib/stores/i18n.js';

// Test data
const testDate = new Date('2024-01-15');
const testDateString = '2024-01-15';

console.log('=== Testing Date Formatting Functions ===');

// Test formatDateByLanguage
console.log('\n--- formatDateByLanguage ---');
console.log('English (explicit):', formatDateByLanguage(testDate, 'en'));
console.log('Thai (explicit):', formatDateByLanguage(testDate, 'th'));
console.log('Auto-detect (should use store):', formatDateByLanguage(testDate));

// Test formatShortDateByLanguage  
console.log('\n--- formatShortDateByLanguage ---');
console.log('English (explicit):', formatShortDateByLanguage(testDate, 'en'));
console.log('Thai (explicit):', formatShortDateByLanguage(testDate, 'th'));
console.log('Auto-detect (should use store):', formatShortDateByLanguage(testDate));

// Test with string input
console.log('\n--- String Input Tests ---');
console.log('String input (EN):', formatDateByLanguage(testDateString, 'en'));
console.log('String input (TH):', formatDateByLanguage(testDateString, 'th'));

// Test edge cases
console.log('\n--- Edge Cases ---');
console.log('Null date (EN):', formatDateByLanguage(null, 'en'));
console.log('Null date (TH):', formatDateByLanguage(null, 'th'));
console.log('Invalid date (EN):', formatDateByLanguage('invalid-date', 'en'));
console.log('Invalid date (TH):', formatDateByLanguage('invalid-date', 'th'));
