// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-outside-of-docker-compose
{
	"name": "Node",
	"dockerComposeFile": "docker-compose.yml",
	"service": "app",
	"workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",
	// Use this environment variable if you need to bind mount your local source code into a new container.
	"remoteEnv": {
		"LOCAL_WORKSPACE_FOLDER": "${localWorkspaceFolder}",
		"LOCAL_WORKSPACE_FOLDERBASENAME": "${localWorkspaceFolderBasename}"
	},
	"features": {
		"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {
			"version": "latest",
			"enableNonRootDocker": "true",
			"moby": "true"
		}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"michelemelluso.gitignore",
				"svelte.svelte-vscode",
				"bradlc.vscode-tailwindcss",
				"ardenivanov.svelte-intellisense",
				"GitHub.copilot"
			]
		}
	},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [
		8000
	]
	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "sudo nginx-debug"
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}