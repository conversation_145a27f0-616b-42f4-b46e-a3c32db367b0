<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import {
		CloseCircleOutline,
		ClipboardListOutline,
		FilterOutline,
		CloseOutline,
		RefreshOutline,
		ShieldCheckOutline,
		HourglassOutline,
		ExclamationCircleOutline,
		ListOutline
	} from 'flowbite-svelte-icons';
	import { Button, Dropdown, DropdownItem, Badge, Modal } from 'flowbite-svelte';
	import { flyAndScale } from '$lib/utils';
	import SveltyPicker from 'svelty-picker';

	export let customer_policies: any;

	// Policy status order for sorting
	const statusOrder = ['active', 'pending', 'nearly_expired', 'expired', 'cancelled', 'inactive'];

	// Note: showMore functionality replaced by filtering system

	// Filter state variables
	let searchQuery = '';
	let selectedStatuses: Set<string> = new Set(['All']);
	let selectedTypes: Set<string> = new Set(['All']);
	let startDateFilter: Date | null = null;
	let endDateFilter: Date | null = null;

	// Date range variable for svelty-picker range binding
	let dateRange = '';

	// Modal state for policy details
	let policyDetailModal = false;
	let selectedPolicy = null;

	// Mock data for policy details modal (matching design specification)
	const mockPolicyDetails = {
		member: {
			name: 'Ms. Piyada Juktos',
			code: 'MEM007'
		},
		policy: {
			number: 'POL007',
			certificateNumber: 'CERT007',
			insurancePlan: 'Young Professional Plan',
			effectiveFrom: 'January 1, 2025',
			effectiveTo: 'December 31, 2025',
			insuranceCompany: 'Life Insurance A Ltd.',
			contractCompany: 'PQR Corporation'
		},
		mainBenefits: {
			amount: 300000,
			currency: 'THB',
			period: 'per year',
			remaining: 280000,
			description: 'Comprehensive health insurance with extensive medical coverage'
		},
		detailedCoverage: [
			{
				type: 'Young Professional Room',
				description: 'Young Professional Suite',
				limit: 3000,
				annualLimit: 300000,
				remaining: 280000
			},
			{
				type: 'Young Professional Doctor',
				description: 'Young Professional Specialist',
				limit: 4000,
				annualLimit: 300000,
				remaining: 280000
			}
		],
		contractConditions: {
			memberType: 'Young Adult',
			startDate: '1/1/2025',
			ageRange: 'Age limit: 18-35 years'
		},
		claimsHistory: [
			{
				claimNo: 'CLM014',
				status: 'Paid',
				type: 'Outpatient',
				diagnosis: 'Myopia',
				paidAmount: 8500,
				claimedAmount: 10000,
				difference: 1500,
				serviceDate: '27/09/2024',
				claimDate: '30/09/2024',
				settlementDate: '31/10/2024',
				provider: 'Samitivej Sukhumvit Hospital',
				policyNumber: 'POL007'
			},
			{
				claimNo: 'CLM015',
				status: 'Pending',
				type: 'Emergency',
				diagnosis: 'Fever Fracture',
				paidAmount: 14000,
				claimedAmount: 15000,
				difference: 1000,
				serviceDate: '31/10/2024',
				claimDate: '31/10/2024',
				settlementDate: '31/10/2024',
				provider: 'St. Louis Hospital',
				policyNumber: 'POL007'
			},
			{
				claimNo: 'CLM016',
				status: 'Rejected',
				type: 'Emergency',
				diagnosis: 'Fever Fracture',
				paidAmount: 14000,
				claimedAmount: 15000,
				difference: 1000,
				serviceDate: '31/10/2024',
				claimDate: '31/10/2024',
				settlementDate: '31/10/2024',
				provider: 'St. Louis Hospital',
				policyNumber: 'POL007'
			}
		]
	};

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Convert date range string to Date objects for filtering logic
	$: {
		if (dateRange && typeof dateRange === 'string' && dateRange.includes(' ~ ')) {
			const [startStr, endStr] = dateRange.split(' ~ ');
			startDateFilter = startStr ? new Date(startStr) : null;
			endDateFilter = endStr ? new Date(endStr) : null;
		} else {
			startDateFilter = null;
			endDateFilter = null;
		}
	}

	// Filter logic
	$: filteredPolicies = customer_policies.policies.filter((policy: any) => {
		// Search filter
		const searchMatch =
			!searchQuery ||
			policy.product?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
			policy.product?.product_type?.toLowerCase().includes(searchQuery.toLowerCase());

		// Status filter
		const statusMatch = selectedStatuses.has('All') || selectedStatuses.has(policy.policy_status);

		// Type filter
		const typeMatch = selectedTypes.has('All') || selectedTypes.has(policy.product?.product_type);

		// Date range filter
		let dateMatch = true;
		if (startDateFilter || endDateFilter) {
			const policyStartDate = policy.start_date ? new Date(policy.start_date) : null;
			const policyEndDate = policy.end_date ? new Date(policy.end_date) : null;

			if (startDateFilter && policyStartDate) {
				dateMatch = dateMatch && policyStartDate >= startDateFilter;
			}
			if (endDateFilter && policyEndDate) {
				dateMatch = dateMatch && policyEndDate <= endDateFilter;
			}
		}

		return searchMatch && statusMatch && typeMatch && dateMatch;
	});

	// Sort filtered policies by status
	$: sortedVisiblePolicies = filteredPolicies.sort((a: any, b: any) => {
		const aIndex = statusOrder.indexOf(a.policy_status.toLowerCase());
		const bIndex = statusOrder.indexOf(b.policy_status.toLowerCase());
		return aIndex - bIndex;
	});

	// Available filter options
	$: availableStatuses = [
		'All',
		...Array.from(new Set(customer_policies.policies.map((p: any) => p.policy_status)))
	] as string[];
	$: availableTypes = [
		'All',
		...Array.from(
			new Set(customer_policies.policies.map((p: any) => p.product?.product_type).filter(Boolean))
		)
	] as string[];

	// Policy type icons mapping
	type PolicyTypeKey =
		| 'compulsory_motor'
		| 'car'
		| 'home'
		| 'cancer'
		| 'health_accident_travel'
		| 'cyber'
		| 'business'
		| 'general'
		| 'basic'
		| 'premium'
		| 'executive';
	const typeIcons: Record<PolicyTypeKey, string> & Record<string, string> = {
		compulsory_motor: '🚗',
		car: '🚙',
		home: '🏠',
		cancer: '❤️',
		health_accident_travel: '🏥',
		cyber: '⚡︎',
		business: '🏢',
		general: '📄',
		basic: '📋',
		premium: '⭐',
		executive: '💼'
	};

	// Status color classes for badges
	type StatusKey = 'active' | 'inactive' | 'nearly_expired' | 'expired' | 'pending' | 'cancelled';
	const statusColors: Record<StatusKey, string> & Record<string, string> = {
		active: 'bg-green-100 text-green-500 border-green-200',
		pending: 'bg-blue-100 text-blue-500 border-blue-200',
		nearly_expired: 'bg-yellow-100 text-yellow-500 border-yellow-200',
		expired: 'bg-red-100 text-red-500 border-red-200',
		inactive: 'bg-gray-100 text-gray-500 border-gray-200',
		cancelled: 'bg-gray-100 text-gray-500 border-gray-200'
	};

	// Function to format date
	const displayDateDraft = (timestamp: string) => {
		const displayCreated = new Date(timestamp);

		// Format each part separately
		const day = displayCreated.getDate().toString().padStart(2, '0');
		const month = displayCreated.toLocaleString('en-US', { month: 'short' });
		const year = displayCreated.getFullYear();

		// Add hour and minute in 24-hour format (currently unused)
		// const hour = displayCreated.getHours().toString().padStart(2, '0');
		// const minute = displayCreated.getMinutes().toString().padStart(2, '0');

		// Combine in desired format
		return `${day} ${month} ${year}`;
		// return `${day} ${month} ${year} ${hour}:${minute}`;
	};

	// Filter helper functions
	function toggleFilter(value: string, selectedSet: Set<string>): Set<string> {
		const newSet = new Set(selectedSet);

		if (value === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(value)) {
				newSet.delete(value);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(value);
			}
		}

		return newSet;
	}

	function clearAllFilters() {
		searchQuery = '';
		selectedStatuses = new Set(['All']);
		selectedTypes = new Set(['All']);
		startDateFilter = null;
		endDateFilter = null;
		dateRange = '';
	}

	// Enhanced clear filters function with keyboard support
	function handleClearFilters(event: KeyboardEvent | MouseEvent) {
		// Handle keyboard events (Enter and Space)
		if (event instanceof KeyboardEvent) {
			if (event.key !== 'Enter' && event.key !== ' ') {
				return;
			}
			event.preventDefault();
		}
		clearAllFilters();
	}

	// Reactive statement that properly tracks all filter dependencies
	$: activeFilterCount = (() => {
		let count = 0;
		if (searchQuery && searchQuery.trim() !== '') count++;
		if (!selectedStatuses.has('All')) count++;
		if (!selectedTypes.has('All')) count++;
		if (startDateFilter || endDateFilter) count++;
		return count;
	})();

	// Handle policy card click
	function handlePolicyClick(policy) {
		selectedPolicy = policy;
		policyDetailModal = true;
	}

	// Handle keyboard events for accessibility
	function handlePolicyKeydown(event, policy) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handlePolicyClick(policy);
		}
	}
</script>

<!-- <p class="text-sm text-gray-500 dark:text-gray-400">
<b>Ticket:</b>
</p> -->
<!-- Policy Statistics-->
<div
	class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5"
>
	<!-- Total Policies -->
	<div class="flex items-center rounded-lg bg-gray-100 p-4">
		<div class="mr-3 rounded-full bg-gray-200 p-2">
			<ListOutline class="h-6 w-6 text-gray-700" />
		</div>
		<div>
			<p class="text-sm text-gray-500">{t('total_policies')}</p>
			<p class="text-2xl font-bold">{customer_policies.statistics.total_policies}</p>
		</div>
	</div>

	<!-- Active Policies -->
	<div class="flex items-center rounded-lg bg-green-100 p-4">
		<div class="mr-3 rounded-full bg-green-200 p-2">
			<ShieldCheckOutline class="h-6 w-6 text-gray-700" />
		</div>
		<div>
			<p class="text-sm text-green-500">{t('active')}</p>
			<p class="text-2xl font-bold text-green-500">
				{customer_policies.statistics.active_policies}
			</p>
		</div>
	</div>

	<!-- Waiting Period Policies -->
	<div class="flex items-center rounded-lg bg-blue-100 p-4">
		<div class="mr-3 rounded-full bg-blue-200 p-2">
			<HourglassOutline class="h-6 w-6 text-blue-700" />
		</div>
		<div>
			<p class="text-sm text-blue-500">{t('pending')}</p>
			<p class="text-2xl font-bold text-blue-500">
				{customer_policies.statistics.waiting_period_policies}
			</p>
		</div>
	</div>

	<!-- Nearly Expired Policies -->
	<div class="flex items-center rounded-lg bg-yellow-100 p-4">
		<div class="mr-3 rounded-full bg-yellow-200 p-2">
			<ExclamationCircleOutline class="h-6 w-6 text-yellow-700" />
		</div>
		<div>
			<p class="text-sm text-yellow-500">{t('nearly_expired')}</p>
			<p class="text-2xl font-bold text-yellow-500">
				{customer_policies.statistics.nearly_expired_policies}
			</p>
		</div>
	</div>

	<!-- Expired Policies -->
	<div class="flex items-center rounded-lg bg-red-100 p-4">
		<div class="mr-3 rounded-full bg-red-200 p-2">
			<CloseCircleOutline class="h-6 w-6 text-red-700" />
		</div>
		<div>
			<p class="text-sm text-red-500">{t('expired')}</p>
			<p class="text-2xl font-bold text-red-500">{customer_policies.statistics.expired_policies}</p>
		</div>
	</div>
</div>

<!-- Policy Filtering Section -->
<div class="mb-6 space-y-4">
	<!-- Filter Controls -->
	<div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
		<!-- Left side - Filter dropdowns and search -->
		<div class="flex flex-col gap-3 sm:flex-row sm:items-center">
			<!-- Search Input - Temporarily disabled but preserved for future use -->
			<!-- <div class="relative">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<input
					type="text"
					placeholder={t('search_policies')}
					bind:value={searchQuery}
					class="w-full rounded-lg border border-gray-300 px-3 py-2 pl-10 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-64"
				/>
			</div> -->

			<!-- Status Filter -->
			<div class="relative">
				<Button color="light" class="w-full justify-left sm:w-auto">
					<FilterOutline class="mr-2 h-4 w-4" />
					{t('policy_filter_by_status')}
					{#if !selectedStatuses.has('All')}
						<Badge color="blue" class="ml-2">{selectedStatuses.size}</Badge>
					{/if}
				</Button>
				<Dropdown class="w-48">
					{#each availableStatuses as status}
						<DropdownItem
							on:click={() => (selectedStatuses = toggleFilter(status, selectedStatuses))}
							class="flex items-center"
						>
							<input type="checkbox" checked={selectedStatuses.has(status)} class="mr-2" readonly />
							{status === 'All' ? t('policy_filter_all_statuses') : t(status.toLowerCase())}
						</DropdownItem>
					{/each}
				</Dropdown>
			</div>

			<!-- Type Filter -->
			<div class="relative">
				<Button color="light" class="w-full justify-left sm:w-auto">
					<FilterOutline class="mr-2 h-4 w-4" />
					{t('policy_filter_by_type')}
					{#if !selectedTypes.has('All')}
						<Badge color="blue" class="ml-2">{selectedTypes.size}</Badge>
					{/if}
				</Button>
				<Dropdown class="w-60">
					{#each availableTypes as type}
						<DropdownItem
							on:click={() => (selectedTypes = toggleFilter(type, selectedTypes))}
							class="flex items-center"
						>
							<input type="checkbox" checked={selectedTypes.has(type)} class="mr-2" readonly />
							{type === 'All' ? t('policy_filter_all_types') : type}
						</DropdownItem>
					{/each}
				</Dropdown>
			</div>
		</div>

		<!-- Right side - Date filters and clear button -->
		<div class="flex flex-col gap-3 sm:flex-row sm:items-center">
			<!-- Date Range Filter -->
			<!-- <div class="flex flex-col gap-2 sm:flex-row sm:items-center">
				<div class="svelty-picker-container w-full sm:w-auto">
					<SveltyPicker
						placeholder="{t('start_date')} - {t('end_date')}"
						bind:value={dateRange}
						inputClasses="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
						format="dd M yyyy"
						isRange={true}
                        todayBtn={false}
                        clearBtn={false}
					/>
				</div>
			</div> -->

			<!-- Clear Filters Button -->
			{#if activeFilterCount > 0}
				<Button
					color="light"
					on:click={clearAllFilters}
					on:keydown={handleClearFilters}
					class="w-full transition-all duration-200 hover:border-gray-300 hover:bg-gray-100 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-auto"
					aria-label="Clear all active filters and reset view"
					title="Clear all filters ({activeFilterCount} active)"
				>
					<RefreshOutline class="mr-2 h-4 w-4" />
					{t('policy_filter_clear_all')}
					<!-- {#if activeFilterCount > 1}
						<span class="ml-1 text-xs text-gray-500">({activeFilterCount})</span>
					{/if} -->
				</Button>
			{/if}
		</div>
	</div>

	<!-- Active Filters Display -->
	{#if activeFilterCount > 0}
		<div class="flex flex-wrap gap-2">
			<span class="text-sm font-medium text-gray-700">{t('policy_filter_active')}:</span>

			<!-- {#if searchQuery}
				<Badge color="dark" class="flex items-center">
					{t('policy_filter_search')}: "{searchQuery}"
					<button
						on:click={() => (searchQuery = '')}
						class="hover:text-dark-800 ml-1"
						aria-label="Remove search filter"
					>
						<CloseOutline class="h-3 w-3" />
					</button>
				</Badge>
			{/if} -->

			{#if !selectedStatuses.has('All')}
				{#each Array.from(selectedStatuses) as status}
					<Badge color="dark" class="flex items-center border border-gray-300 rounded-full bg-gray-100 text-gray-800">
						{t(status.toLowerCase())}
						<button
							on:click={() => (selectedStatuses = toggleFilter(status, selectedStatuses))}
							class="ml-1 hover:text-gray-800"
							aria-label="Remove status filter"
						>
							<CloseOutline class="h-3 w-3" />
						</button>
					</Badge>
				{/each}
			{/if}

			{#if !selectedTypes.has('All')}
				{#each Array.from(selectedTypes) as type}
					<Badge color="dark" class="flex items-center border border-gray-300 rounded-full bg-gray-100 text-gray-800">
						{type}
						<button
							on:click={() => (selectedTypes = toggleFilter(type, selectedTypes))}
							class="hover:text-dark-800 ml-1"
							aria-label="Remove type filter"
						>
							<CloseOutline class="h-3 w-3" />
						</button>
					</Badge>
				{/each}
			{/if}

			{#if startDateFilter}
				<Badge color="dark" class="flex items-center">
					{t('start_date')}: {startDateFilter.toLocaleDateString()}
					<button
						on:click={() => (startDateFilter = null)}
						class="hover:text-dark-800 ml-1"
						aria-label="Remove start date filter"
					>
						<CloseOutline class="h-3 w-3" />
					</button>
				</Badge>
			{/if}

			{#if endDateFilter}
				<Badge color="dark" class="flex items-center">
					{t('end_date')}: {endDateFilter.toLocaleDateString()}
					<button
						on:click={() => (endDateFilter = null)}
						class="hover:text-dark-800 ml-1"
						aria-label="Remove end date filter"
					>
						<CloseOutline class="h-3 w-3" />
					</button>
				</Badge>
			{/if}
		</div>
	{/if}
</div>

<section
	class="grid grid-cols-1 gap-6
           py-6
           sm:grid-cols-1
           md:grid-cols-1
           lg:grid-cols-2
           xl:grid-cols-3
           2xl:grid-cols-3"
	aria-label="Policy cards grid"
>
	{#if sortedVisiblePolicies.length === 0}
		<div class="col-span-full flex flex-col items-center justify-center py-12 text-center">
			<div class="mb-4 rounded-full bg-gray-100 p-6">
				<FilterOutline class="h-12 w-12 text-gray-400" />
			</div>
			<h3 class="mb-2 text-lg font-medium text-gray-900">{t('policy_filter_no_found')}</h3>
			<!-- <p class="mb-4 text-gray-500">
				Try adjusting your filters or search terms to find more policies.
			</p> -->
			{#if activeFilterCount > 0}
				<Button
					color="blue"
					on:click={clearAllFilters}
					on:keydown={handleClearFilters}
					class="transition-all duration-200 hover:bg-blue-700 focus:ring-2 focus:ring-blue-300"
					aria-label="Clear all active filters and reset view"
					title="Clear all filters ({activeFilterCount} active)"
				>
					<RefreshOutline class="mr-2 h-4 w-4" />
					{t('policy_filter_clear_all')}
				</Button>
			{/if}
		</div>
	{:else}
		{#each sortedVisiblePolicies as policy}
			<button
				class="flex min-h-[480px] w-full transform
               cursor-pointer flex-col justify-between
               rounded-lg border
               border-gray-100 bg-white
               p-6 text-left
               shadow-md transition-all duration-300
               ease-in-out hover:scale-105 hover:shadow-lg"
				on:click={() => handlePolicyClick(policy)}
				on:keydown={(e) => handlePolicyKeydown(e, policy)}
				aria-label="View detailed information for {policy.product?.name ||
					'policy'} - Status: {policy.policy_status}"
			>
				<!-- Policy Header -->
				<div>
					<div class="mb-3 flex items-center justify-between">
						<h2
							id="policy-title"
							class="flex items-center gap-2 text-lg font-semibold text-gray-900"
						>
							<span class="text-2xl" aria-hidden="true">
								<!-- {typeIcons[policy.product?.product_type?.toLowerCase()] || typeIcons.general} -->
								<ClipboardListOutline class="h-6 w-6" />
							</span>
						</h2>
						<div class="flex flex-row items-end gap-1">
							<span
								class="rounded-full border px-3 py-1 text-xs font-medium
                            {statusColors[policy.policy_status?.toLowerCase()] ||
									statusColors.inactive}"
								aria-label="Policy status: {policy.policy_status}"
							>
								<!-- {policy.policy_status?.toUpperCase().replace(/_/g, ' ')} -->
								{t(policy.policy_status?.toLowerCase())}
							</span>
							<!-- <span
								class="rounded-full border border-violet-200 bg-violet-100 px-2 py-1
                            text-xs font-medium text-violet-500"
							>
								VIP
							</span> -->
						</div>
					</div>

					<div class="mb-3 space-y-1">
						<h2 class="flex items-center gap-2 text-lg font-semibold text-gray-900">
							{policy.product?.name || 'N/A'}
						</h2>
					</div>

					<!-- Policy Numbers -->
					<div class="mb-3 space-y-1">
						<p class="flex items-center justify-between text-sm text-gray-600">
							<span class="text-sm text-gray-500">{t('policy_no')}</span>
							<span class="text-right text-sm font-medium text-gray-900"> ? </span>
						</p>
						<p class="flex items-center justify-between text-sm text-gray-600">
							<span class="text-sm text-gray-500">{t('policy_certificate_no')}</span>
							<span class="text-right text-sm font-medium text-gray-900"> ? </span>
						</p>
						<!-- {#if policy.InsurerCardNo} -->
						<p class="flex items-center justify-between text-sm text-gray-600">
							<span class="text-sm text-gray-500">{t('policy_insurance_card_no')}</span>
							<span class="text-right text-sm font-medium text-gray-900"> ? </span>
						</p>
						<!-- {/if} -->
						<!-- {#if policy.StaffNo} -->
						<p class="flex items-center justify-between text-sm text-gray-600">
							<span class="text-sm text-gray-500">{t('policy_agent_no')}</span>
							<span class="text-right text-sm font-medium text-gray-900"> ? </span>
						</p>
						<!-- {/if} -->
					</div>

					<!-- Policy Details -->
					<div class="flex-grow space-y-4">
						<!-- Member & Card Information -->
						<div class="space-y-2 rounded-lg bg-gray-50 p-3">
							<h3 class="text-xs font-semibold uppercase tracking-wide text-gray-700">
								{t('policy_member_section')}
							</h3>
							<div class="grid grid-cols-2 gap-2 text-xs">
								<div>
									<span class="text-gray-500">{t('policy_member_type')}</span>
									<div class="font-medium text-gray-900">?</div>
								</div>
								<div class="ml-3">
									<span class="text-gray-500">{t('policy_member_card_type')}</span>
									<div class="font-medium text-gray-900">?</div>
								</div>
							</div>
						</div>

						<!-- Coverage & Premium Information -->
						<!-- {#if policy.CoverageAmount || policy.Premium} -->
						<div class="space-y-2 rounded-lg bg-blue-50 p-3">
							<h3 class="text-xs font-semibold uppercase tracking-wide text-blue-700">
								{t('policy_coverage_section')}
							</h3>
							<div class="space-y-2 text-xs">
								<!-- {#if policy.CoverageAmount} -->
								<div class="flex items-center justify-between">
									<span class="text-blue-600">{t('policy_coverage')}</span>
									<span class="font-semibold text-blue-900">
										{policy.product.coverage?.toLocaleString('th-TH', {
											style: 'currency',
											currency: 'THB'
										}) || 'N/A'}
									</span>
								</div>
								<!-- {/if} -->
								<!-- {#if policy.Premium} -->
								<div class="flex items-center justify-between">
									<span class="text-blue-600">{t('policy_premium')}</span>
									<span class="font-semibold text-blue-900">
										{policy.product.premium?.toLocaleString('th-TH', {
											style: 'currency',
											currency: 'THB'
										}) || 'N/A'}
									</span>
								</div>
								<!-- {/if} -->
							</div>
						</div>
						<!-- {/if} -->

						<!-- Plan & Date Information -->
						<div class="space-y-2 rounded-lg bg-green-50 p-3">
							<h3 class="text-xs font-semibold uppercase tracking-wide text-green-700">
								{t('policy_coverage_period')}
							</h3>
							<div class="space-y-2 text-xs">
								<div class="flex items-center justify-between">
									<span class="text-green-600">{t('start_date')}</span>
									<span class="font-medium text-green-900">
										{policy.start_date ? displayDateDraft(policy.start_date) : 'N/A'}
									</span>
								</div>
								<div class="flex items-center justify-between">
									<span class="text-green-600">{t('end_date')}</span>
									<span class="font-medium text-green-900">
										{policy.end_date ? displayDateDraft(policy.end_date) : 'N/A'}
									</span>
								</div>
							</div>
						</div>

						<!-- Company Information -->
						<div class="space-y-1 border-t border-gray-100 pt-4">
							<div class="flex items-center justify-between">
								<span class="text-sm text-gray-500">{t('policy_insurer_name')}</span>
								<span class="rounded bg-gray-100 px-2 py-1 text-sm text-gray-700"> ? </span>
							</div>
						</div>

						<!-- Plan Description -->
						<div>
							<div class="space-y-1">
								<p class="ml-2 line-clamp-2 flex justify-between text-xs text-gray-600">
									{t('policy_plan_no')}
									<span class="rounded bg-gray-100 px-2 py-1 text-gray-700"> ? </span>
								</p>
								<p class="ml-2 line-clamp-2 flex justify-between text-xs text-gray-600">
									{t('policy_plan_code')}
									<span class="rounded bg-gray-100 px-2 py-1 text-gray-700"> ? </span>
								</p>
							</div>
						</div>
					</div>
				</div></button
			>
		{/each}
	{/if}
</section>

<!-- See More / See Less button -->
<!-- {#if customer_policies.policies.length > 3}
    <div class="text-center">
        <button
            on:click={() => (showMore = !showMore)}
            class="px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-700 transition"
        >
            {showMore ? t('see_less') : t('see_more')}
        </button>
    </div>
{/if} -->

<!-- Policy Detail Modal -->
<Modal
	bind:open={policyDetailModal}
	size="xl"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto bg-gray-100"
>
	<div slot="header" class="flex flex-col gap-1">
		<div class="flex items-center justify-between">
			<h2 class="flex items-center gap-3 text-lg font-semibold">
				<ClipboardListOutline class="h-6 w-6" />
				{t('policy_modal_header_title')}
				<span
					class="rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800"
				>
					{t('active')}
				</span>
			</h2>
		</div>
		<div class="flex flex-col gap-1 text-sm text-gray-600 sm:flex-row sm:gap-4">
			<span><strong>
				{t('policy_modal_header_member_name')}
			</strong> {mockPolicyDetails.member.name}</span>
			<span><strong>
				{t('policy_modal_header_member_code')}
			</strong> {mockPolicyDetails.member.code}</span>
		</div>
	</div>

	{#if selectedPolicy}
		<div class="space-y-4 p-1">
			<!-- Policy Information Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white pb-4">
				<h3 class="mb-3 p-4 text-lg font-semibold text-gray-700">
					{t('policy_modal_info_title')}
				</h3>
				<div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_policy_no')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>{mockPolicyDetails.policy.number}</span
						>
					</div>
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_certificate_no')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>{mockPolicyDetails.policy.certificateNumber}</span
						>
					</div>
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_insurance_plan')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>{mockPolicyDetails.policy.insurancePlan}</span
						>
					</div>
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_insurance_company')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>{mockPolicyDetails.policy.insuranceCompany}</span
						>
					</div>
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_effective_from')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>{mockPolicyDetails.policy.effectiveFrom}</span
						>
					</div>
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_effective_to')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>{mockPolicyDetails.policy.effectiveTo}</span
						>
					</div>
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_contract_company')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>{mockPolicyDetails.policy.contractCompany}</span
						>
					</div>
					<div class="flex justify-between px-4">
						<span class="text-gray-600">
							{t('policy_modal_info_premium')}
						</span>
						<span class="text-right font-semibold text-gray-900"
							>10,000</span
						>
					</div>
				</div>
			</div>

			<!-- Main Benefits Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
				<h3 class="mb-3 text-lg font-semibold text-gray-700">
					{t('policy_modal_benefit_title')}
				</h3>
				<div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
					<!-- Left side - Description -->
					<div class="mb-3 lg:mb-0 lg:flex-1">
						<h4 class="mb-1 text-lg text-base font-semibold text-gray-900">
							{mockPolicyDetails.policy.insurancePlan}
						</h4>
						<p class="text-md leading-relaxed text-gray-600">
							{mockPolicyDetails.mainBenefits.description}
						</p>
					</div>

					<!-- Right side - Amount -->
					<div
						class="rounded-lg border border-gray-200 bg-gray-50 p-3 shadow-sm lg:ml-6 lg:text-right"
					>
						<div class="mb-1">
							<span class="text-xl font-bold text-blue-600 sm:text-xl lg:text-xl">
								{mockPolicyDetails.mainBenefits.currency}
								{mockPolicyDetails.mainBenefits.amount.toLocaleString()}
							</span>
						</div>
						<span class="mb-1 text-sm text-blue-700">
							{mockPolicyDetails.mainBenefits.period}
						</span>
						<div class="text-sm text-green-600">
							<span class="font-medium">
								{t('policy_modal_benefit_remaining')}
							</span>
							<span class="font-bold">
								{mockPolicyDetails.mainBenefits.currency}
								{mockPolicyDetails.mainBenefits.remaining.toLocaleString()}
							</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Detailed Coverage Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
				<h3 class="mb-3 text-lg font-semibold text-gray-700">
					{t('policy_modal_coverage_title')}
				</h3>
				<div class="space-y-3">
					{#each mockPolicyDetails.detailedCoverage as coverage}
						<div class="rounded-lg border border-gray-200 p-4 shadow-sm">
							<div class="mb-0 flex flex-col items-start justify-between lg:flex-row">
								<div class="mb-3 flex-1 lg:mb-0">
									<h4 class="font-semibold text-gray-900">{coverage.type}</h4>
									<p class="text-sm text-gray-600">{coverage.description}</p>
								</div>

								<!-- Financial Elements positioned at top-right -->
								<div
									class="flex flex-col gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3 text-sm shadow-sm sm:flex-row sm:justify-between md:gap-6 lg:justify-end lg:gap-6"
								>
									<div class="flex flex-col sm:text-right">
										<div class="text-sm text-gray-600">
											{t('policy_modal_coverage_limit_visit')}
										</div>
										<div class="text-lg font-semibold text-green-600">
											THB {coverage.limit.toLocaleString()}
										</div>
									</div>
									<div class="flex flex-col sm:text-right">
										<div class="text-sm text-gray-600">
											{t('policy_modal_coverage_limit_annual')}
										</div>
										<div class="text-lg font-semibold text-green-600">
											THB {coverage.annualLimit.toLocaleString()}
										</div>
									</div>
									<div class="flex flex-col sm:text-right">
										<div class="text-sm text-gray-600">
											{t('policy_modal_coverage_remaining')}
										</div>
										<div class="text-lg font-semibold text-green-600">
											THB {coverage.remaining.toLocaleString()}
										</div>
									</div>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Contract Conditions Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
				<div class="flex items-center justify-between">
					<h3 class="mb-3 text-lg font-semibold text-gray-700">
						{t('policy_modal_condition_title')}
					</h3>
				</div>
				<div class="rounded-lg border border-gray-200 py-4 shadow-sm">
					<div class="mb-2 flex flex-col gap-2 px-4 sm:flex-row sm:items-center sm:gap-3">
						<h4 class="font-semibold text-gray-900">
							{mockPolicyDetails.contractConditions.memberType}
						</h4>
						<div class="flex flex-wrap gap-2">
							<!-- Status Badge -->
							<span
								class="rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800"
							>
								{t('active')}
							</span>
						</div>
					</div>
					<div class="space-y-2">
						<div class="flex justify-start px-4">
							<span class="w-32 text-md text-gray-600">
								{t('policy_modal_condition_remarks')}
							</span>
							<span class="font-semibold text-md text-gray-900"
								>{mockPolicyDetails.contractConditions.ageRange}</span
							>
						</div>
						<div class="flex justify-start px-4">
							<span class="w-32 text-md text-gray-600">
								{t('policy_modal_condition_effective_date')}
							</span>
							<span class="font-semibold text-md text-gray-900">
								{mockPolicyDetails.contractConditions.startDate}
							</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Claims History Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
				<div class="mb-4 flex flex-col items-start justify-between sm:flex-row sm:items-center">
					<h3 class="mb-2 text-lg font-semibold text-gray-700 sm:mb-0">
						{t('policy_modal_claims_title')}
					</h3>
					<div class="text-sm text-gray-600">
						{t('policy_modal_claims_total')}
						{mockPolicyDetails.claimsHistory.length}
						{t('policy_modal_claims_total_unit')}
					</div>
				</div>
				<div class="space-y-3">
					{#each mockPolicyDetails.claimsHistory as claim}
						<div class="rounded-lg border border-gray-200 p-4 shadow-sm">
							<!-- Claim Header -->
							<div class="mb-3 flex flex-col items-start justify-between lg:flex-row">
								<div class="mb-3 flex-1 lg:mb-0">
									<div class="mb-2 flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
										<h4 class="text-lg font-semibold text-gray-900">
											{t('policy_modal_claims_no')}
											{claim.claimNo}
										</h4>
										<div class="flex flex-wrap gap-2">
											<!-- Status Badge -->
											<span
												class="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium
												{claim.status === 'Paid'
													? 'border-green-200 bg-green-100 text-green-800'
													: claim.status === 'Reimbursed'
														? 'border-yellow-200 bg-yellow-100 text-yellow-800'
														: claim.status === 'Pending'
															? 'border-blue-200 bg-blue-100 text-blue-800'
															: claim.status === 'Rejected'
																? 'border-red-200 bg-red-100 text-red-800'
																: 'border-blue-200 bg-blue-100 text-blue-800'}"
											>
												{t('policy_modal_claims_status_' + claim.status.toLowerCase())}
											</span>
											<!-- Type Badge -->
											<span
												class="inline-flex items-center rounded-full border border-gray-200 bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800"
											>
												{t('policy_modal_claims_type_' + claim.type.toLowerCase())}
											</span>
										</div>
									</div>
									<p class="text-gray-700">{claim.diagnosis}</p>
								</div>

								<!-- Amount Information -->
								<div
									class="flex flex-col gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3 text-sm shadow-sm sm:flex-row sm:justify-between md:gap-6 lg:justify-end lg:gap-6"
								>
									<div class="flex flex-col sm:text-right">
										<div class="text-sm text-gray-600">
											{t('policy_modal_claims_amount_claimed')}
										</div>
										<div class="text-lg font-semibold text-gray-900">
											THB {claim.claimedAmount.toLocaleString()}
										</div>
									</div>
									<div class="flex flex-col sm:text-right">
										<div class="text-sm text-gray-600">
											{t('policy_modal_claims_amount_paid')}
										</div>
										<div class="text-xl font-semibold text-green-600">
											THB {claim.paidAmount.toLocaleString()}
										</div>
									</div>
									<div class="flex flex-col sm:text-right">
										<div class="text-sm text-gray-600">
											{t('policy_modal_claims_amount_diff')}
										</div>
										<div class="text-lg font-medium text-red-600">
											THB {claim.difference.toLocaleString()}
										</div>
									</div>
								</div>
							</div>

							<!-- Claim Details Grid -->
							<div class="mt-3 grid gap-3 text-sm sm:grid-cols-2 lg:grid-cols-4">
								<!-- Service Date -->
								<div class="flex items-start">
									<svg
										class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
										aria-hidden="true"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
										/>
									</svg>
									<div>
										<div class="font-medium text-gray-700">
											{t('policy_modal_claims_date_service')}
										</div>
										<div class="text-gray-600">{claim.serviceDate}</div>
									</div>
								</div>

								<!-- Claim Date -->
								<div class="flex items-start">
									<svg
										class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
										aria-hidden="true"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
										/>
									</svg>
									<div>
										<div class="font-medium text-gray-700">
											{t('policy_modal_claims_date_claim')}
										</div>
										<div class="text-gray-600">{claim.claimDate}</div>
									</div>
								</div>

								<!-- Settlement Date -->
								<div class="flex items-start">
									<svg
										class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
										aria-hidden="true"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
									<div>
										<div class="font-medium text-gray-700">
											{t('policy_modal_claims_date_settlement')}
										</div>
										<div class="text-gray-600">{claim.settlementDate}</div>
									</div>
								</div>

								<!-- Provider Information -->
								<div class="flex items-start">
									<svg
										class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
										aria-hidden="true"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
										/>
									</svg>
									<div>
										<div class="font-medium text-gray-700">
											{t('policy_modal_claims_provider')}
										</div>
										<div class="text-gray-600">{claim.provider}</div>
									</div>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>
		</div>
	{/if}

	<svelte:fragment slot="footer">
		<Button color="alternative" on:click={() => (policyDetailModal = false)}>
			{t('policy_modal_close')}
		</Button>
	</svelte:fragment>
</Modal>

<style>
	/* Modal content animations with accessibility support */
	:global(.modal-content-section) {
		animation: fadeInUp 0.3s ease-out;
		animation-fill-mode: both;
	}

	:global(.modal-content-section:nth-child(1)) {
		animation-delay: 0.05s;
	}
	:global(.modal-content-section:nth-child(2)) {
		animation-delay: 0.1s;
	}
	:global(.modal-content-section:nth-child(3)) {
		animation-delay: 0.15s;
	}
	:global(.modal-content-section:nth-child(4)) {
		animation-delay: 0.2s;
	}
	:global(.modal-content-section:nth-child(5)) {
		animation-delay: 0.25s;
	}
	:global(.modal-content-section:nth-child(6)) {
		animation-delay: 0.3s;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Respect user's motion preferences */
	@media (prefers-reduced-motion: reduce) {
		:global(.modal-content-section) {
			animation: none;
		}

		@keyframes fadeInUp {
			from,
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
	}

	/* Improve SveltyPicker calendar popup width and spacing for range picker */
	:global(.svelty-picker-container .svelty-picker) {
		min-width: 320px !important;
		width: 100% !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker) {
		min-width: 360px !important;
		width: 360px !important;
		border-radius: 0.5rem !important;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
		border: 1px solid #e5e7eb !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar) {
		padding: 1rem !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .header) {
		margin-bottom: 0.5rem !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .weekdays) {
		margin-bottom: 0.5rem !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .weekdays .weekday) {
		width: 2.5rem !important;
		height: 2.5rem !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		font-size: 0.875rem !important;
		font-weight: 500 !important;
		color: #6b7280 !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .days .day) {
		width: 2.5rem !important;
		height: 2.5rem !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		border-radius: 0.375rem !important;
		font-size: 0.875rem !important;
		cursor: pointer !important;
		transition: all 0.2s ease !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .days .day:hover) {
		background-color: #f3f4f6 !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .days .day.selected) {
		background-color: #3b82f6 !important;
		color: white !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .days .day.today) {
		background-color: #dbeafe !important;
		color: #1d4ed8 !important;
		font-weight: 600 !important;
	}

	/* Range picker specific styles */
	:global(.svelty-picker-container .svelty-picker .picker .calendar .days .day.in-range) {
		background-color: #eff6ff !important;
		color: #1d4ed8 !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .days .day.range-start) {
		background-color: #3b82f6 !important;
		color: white !important;
		border-top-right-radius: 0 !important;
		border-bottom-right-radius: 0 !important;
	}

	:global(.svelty-picker-container .svelty-picker .picker .calendar .days .day.range-end) {
		background-color: #3b82f6 !important;
		color: white !important;
		border-top-left-radius: 0 !important;
		border-bottom-left-radius: 0 !important;
	}
</style>
