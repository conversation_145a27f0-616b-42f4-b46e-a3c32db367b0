// src/stores/i18n.ts
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import en from '$lib/locales/en.json';
import th from '$lib/locales/th.json';

const dictionaries = { en, th } as const;

/* ------------ language store ------------ */
const stored = browser && (localStorage.getItem('lang') as 'en' | 'th' | null);
export const language = writable<'en' | 'th'>(stored ?? 'en');

if (browser) {
	language.subscribe((v) => localStorage.setItem('lang', v));
}

/* ------------ active dictionary ------------ */
export const dict = derived(language, ($l) => dictionaries[$l] ?? dictionaries.en);

/* ------------ helper ------------ */
export function t(key: string): string {
	const d = get(dict);
	return (d && (d as Record<string, string>)[key]) ?? key;
}

/**
 * Format date based on language
 * @param {string|Date} date - Date to format
 * @param {string} language - Language code
 * @returns {string} Formatted date
 */
export function formatDateByLanguage(date, language = SUPPORTED_LANGUAGES.TH) {
  if (!date) return language === SUPPORTED_LANGUAGES.EN ? 'N/A' : 'ไม่มีข้อมูล';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
    }

    if (language === SUPPORTED_LANGUAGES.EN) {
      // English format: Month DD, YYYY
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      return dateObj.toLocaleDateString('en-US', options);
    } else {
      // Thai format with Buddhist Era (BE)
      const day = dateObj.getDate().toString().padStart(2, '0');
      const monthName = dateObj.toLocaleDateString('th-TH', { month: 'long' });
      const yearBE = dateObj.getFullYear() + 543;
      return `${day} ${monthName} ${yearBE}`;
    }
  } catch (error) {
    console.warn('Error formatting date:', error);
    return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
  }
}

/**
 * Format short date based on language
 * @param {string|Date} date - Date to format
 * @param {string} language - Language code
 * @returns {string} Formatted short date
 */
export function formatShortDateByLanguage(date, language = SUPPORTED_LANGUAGES.TH) {
  if (!date) return language === SUPPORTED_LANGUAGES.EN ? 'N/A' : 'ไม่มีข้อมูล';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
    }

    if (language === SUPPORTED_LANGUAGES.EN) {
      // English format: MMM DD, YYYY
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      return dateObj.toLocaleDateString('en-US', options);
    } else {
      // Thai format with Buddhist Era (BE)
      const day = dateObj.getDate().toString().padStart(2, '0');
      const monthName = dateObj.toLocaleDateString('th-TH', { month: 'short' });
      const yearBE = dateObj.getFullYear() + 543;
      return `${day} ${monthName} ${yearBE}`;
    }
  } catch (error) {
    console.warn('Error formatting short date:', error);
    return language === SUPPORTED_LANGUAGES.EN ? 'Invalid date' : 'วันที่ไม่ถูกต้อง';
  }
}